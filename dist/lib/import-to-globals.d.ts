import type MagicString from "magic-string";
interface ImportSpec {
    local: {
        name: string;
        start: number;
        end: number;
    };
    imported?: {
        name: string;
    };
    exported?: {
        name: string;
        start: number;
        end: number;
    };
    isOverwritten?: boolean;
}
interface ASTNode {
    type: string;
    start: number;
    end: number;
    source?: {
        value: string;
    };
    specifiers?: ImportSpec[];
    declaration?: any;
    body?: ASTNode[];
    name?: string;
    scope?: any;
    arguments?: {
        value: string;
    }[];
    callee?: {
        type: string;
    };
    key?: {
        start: number;
        end: number;
        isOverwritten?: boolean;
    };
    value?: {
        start: number;
        end: number;
        isOverwritten?: boolean;
    };
}
interface ImportToGlobalsOptions {
    ast: ASTNode;
    code: MagicString;
    getName: (name: string) => string | undefined;
    getDynamicWrapper: (name: string) => string;
    constBindings: boolean;
}
declare function importToGlobals({ ast, code, getName, getDynamicWrapper, constBindings }: ImportToGlobalsOptions): Promise<boolean>;
export default importToGlobals;
//# sourceMappingURL=import-to-globals.d.ts.map