{"version": 3, "file": "import-to-globals.js", "sourceRoot": "", "sources": ["../../src/lib/import-to-globals.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qDAAwE;AAGxE,IAAI,IAAS,EAAE,WAAgB,CAAC;AAEhC,KAAK,UAAU,OAAO;IACpB,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;0DAChD,eAAe;0DACf,cAAc;KACtB,CAAC,CAAC;AACL,CAAC;AAyBD,SAAS,aAAa,CACpB,IAAa,EACb,cAAmC,EACnC,IAAiB,EACjB,OAA6C,EAC7C,OAAoB;IAEpB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC9D,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAClB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;QACpB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACnC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,cAAc,CAChD,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,EAC9C,IAAI,CACL,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IAClC,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,cAAc,CAAC,IAAY,EAAE,IAAY;IAChD,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,GAAG,IAAI,IAAI,IAAI,EAAE,CAAC;AAC3B,CAAC;AAED,SAAS,cAAc,CACrB,IAAiB,EACjB,IAAa,EACb,IAAgB,EAChB,IAAY,EACZ,SAAsB,EACtB,aAAsB;IAEtB,IAAI,IAAI,CAAC,aAAa;QAAE,OAAO;IAC/B,gEAAgE;IAChE,sEAAsE;IACtE,MAAM,SAAS,GAAG,WAAW,IAAA,iCAAmB,EAAC,IAAI,CAAC,EAAE,CAAC;IACzD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;QAC9B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,SAAS,MAAM,IAAI,KAAK,CAAC,CAAC;QAC7F,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAC3B,CAAC;IACD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QAClC,OAAO;IACT,CAAC;IACD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;QACtG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,SAAS,MAAM,CAAC,CAAC;IACzD,CAAC;SAAM,CAAC;QACN,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;IAC9D,CAAC;IACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC5B,CAAC;AAED,SAAS,eAAe,CAAC,IAAiB,EAAE,IAAa,EAAE,MAAe,EAAE,IAAY;IACtF,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,IAAK,IAAY,CAAC,aAAa,EAAE,CAAC;QACtD,OAAO;IACT,CAAC;IACD,uGAAuG;IACvG,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,IAAI,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACxG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;QACvC,MAAM,CAAC,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC;QAChC,MAAM,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;IACpC,CAAC;SAAM,IAAI,MAAM,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QAC7C,MAAM,UAAU,GAAG,MAAa,CAAC;QACjC,IAAI,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YACpG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,MAAM,CAAC,CAAC;YAC3C,UAAU,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;YACtC,UAAU,CAAC,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC;QAC3C,CAAC;IACH,CAAC;SAAM,CAAC;QACN,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;QAClE,0BAA0B;QACzB,IAAY,CAAC,aAAa,GAAG,IAAI,CAAC;IACrC,CAAC;AACH,CAAC;AAED,SAAS,kBAAkB,CACzB,IAAa,EACb,IAAiB,EACjB,OAA6C,EAC7C,SAAsB,EACtB,aAAsB;IAEtB,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QAC3D,OAAO,KAAK,CAAC;IACf,CAAC;IACD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACxC,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;QACpB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACnC,MAAM,UAAU,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACzD,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;QACzE,CAAC;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YAC3B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QACxF,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,gBAAgB,CACvB,IAAa,EACb,IAAiB,EACjB,OAA6C;IAE7C,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC9D,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO;IACT,CAAC;IACD,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;AAC5E,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAiB,EAAE,IAAa,EAAE,OAAe;IAC3E,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AAChD,CAAC;AAED,SAAS,sBAAsB,CAAC,IAAa;IAC3C,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;QACrC,OAAO,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;IAC5B,CAAC;IACD,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAgB,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5F,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACjC,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,4BAA4B;AAC5B,MAAM,cAAc;IAApB;QACE,kBAAa,GAAG,KAAK,CAAC;QACtB,eAAU,GAAG,KAAK,CAAC;IAgBrB,CAAC;IAdC,KAAK,CAAC,IAAa,EAAE,MAAgB;QACnC,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACxC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,KAAK,wBAAwB,CAAC;QAC9D,CAAC;QACD,IAAI,IAAI,CAAC,aAAa,IAAI,MAAM,EAAE,IAAI,KAAK,oBAAoB,IAAK,MAAc,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC;YAC/F,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACzB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAa,EAAE,MAAgB;QACnC,IAAI,IAAI,CAAC,UAAU,IAAI,MAAM,EAAE,IAAI,KAAK,oBAAoB,EAAE,CAAC;YAC7D,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAC1B,CAAC;IACH,CAAC;CACF;AAUD,KAAK,UAAU,eAAe,CAAC,EAC7B,GAAG,EACH,IAAI,EACJ,OAAO,EACP,iBAAiB,EACjB,aAAa,EACU;IACvB,MAAM,OAAO,EAAE,CAAC;IAChB,IAAI,KAAK,GAAG,IAAA,0BAAY,EAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IACvC,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAkB,CAAC;IAC3C,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAC;IAClC,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,MAAM,SAAS,GAAG,IAAI,GAAG,EAAU,CAAC;IACpC,MAAM,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC;IAE5C,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;QACb,KAAK,MAAM,IAAI,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;YAC5B,IAAI,IAAI,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;gBACtC,SAAS,GAAG,aAAa,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,IAAI,SAAS,CAAC;YACjF,CAAC;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,wBAAwB,EAAE,CAAC;gBAClD,SAAS,GAAG,kBAAkB,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,aAAa,CAAC,IAAI,SAAS,CAAC;YAC7F,CAAC;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAsB,EAAE,CAAC;gBAChD,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YACxC,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,YAAiC,CAAC;IACtC,IAAI,CAAC,GAAG,EAAE;QACR,KAAK,CAAC,IAAa,EAAE,MAAgB;YACnC,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YACnC,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBACxC,YAAY,GAAG,IAAI,CAAC;YACtB,CAAC;YACD,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjC,IAAY,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO;YACT,CAAC;YACD,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACf,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACrB,CAAC;YACD,IAAI,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;gBAC9B,IAAI,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBACvE,IAAI,MAAM,EAAE,IAAI,KAAK,iBAAiB,EAAE,CAAC;wBACvC,IAAI,YAAY,EAAE,CAAC;4BACjB,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE,MAAa,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAE,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;wBACxG,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,MAAO,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAE,CAAC,CAAC;oBACjE,CAAC;gBACH,CAAC;qBAAM,IAAI,IAAI,CAAC,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC5E,+BAA+B;oBAC/B,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,MAAO,EAAE,UAAU,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;oBAC5D,IAAI,cAAc,CAAC,UAAU,IAAI,YAAY,EAAE,CAAC;wBAC9C,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,GAAG,EAAE,kBAAkB,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC;wBACrF,IAAI,YAAY,CAAC,WAAW,EAAE,CAAC;4BAC7B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;wBAClE,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YACD,MAAM,MAAM,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,IAAI,GAAG,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,WAAW,GAAG,IAAI,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACpD,IAAI,WAAW,EAAE,CAAC;gBAChB,kBAAkB,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;gBAC5C,SAAS,GAAG,IAAI,CAAC;gBAChB,IAAY,CAAC,IAAI,EAAE,CAAC;YACvB,CAAC;QACH,CAAC;QACD,KAAK,CAAC,IAAa,EAAE,MAAgB;YACnC,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YACnC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACf,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAC5B,CAAC;QACH,CAAC;KACF,CAAC,CAAC;IAEH,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,kBAAe,eAAe,CAAC"}