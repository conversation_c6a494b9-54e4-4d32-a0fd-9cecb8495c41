"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const magic_string_1 = __importDefault(require("magic-string"));
const pluginutils_1 = require("@rollup/pluginutils");
const import_to_globals_1 = __importDefault(require("./lib/import-to-globals"));
const defaultDynamicWrapper = (id) => `Promise.resolve(${id})`;
function isVirtualModule(id) {
    return id.startsWith("\0");
}
function createPlugin(globals, options = {}) {
    const { include, exclude, dynamicWrapper = defaultDynamicWrapper, constBindings = false } = options;
    if (!globals) {
        throw new TypeError("Missing mandatory option 'globals'");
    }
    let getName = globals;
    const globalsType = typeof globals;
    const isGlobalsObj = globalsType === "object";
    if (isGlobalsObj) {
        const globalsObj = globals;
        getName = function (name) {
            if (Object.prototype.hasOwnProperty.call(globalsObj, name)) {
                return globalsObj[name];
            }
            return undefined;
        };
    }
    else if (globalsType !== "function") {
        throw new TypeError(`Unexpected type of 'globals', got '${globalsType}'`);
    }
    const dynamicWrapperType = typeof dynamicWrapper;
    if (dynamicWrapperType !== "function") {
        throw new TypeError(`Unexpected type of 'dynamicWrapper', got '${dynamicWrapperType}'`);
    }
    async function resolveId(importee, _, options) {
        if (isVirtualModule(importee) || options.isEntry)
            return null;
        const globalName = getName(importee);
        return globalName ? false : null;
    }
    const filter = (0, pluginutils_1.createFilter)(include, exclude);
    function getDebug(context) {
        return (err, message) => {
            if ('debug' in context && typeof context.debug === 'function') {
                context.debug({
                    message,
                    cause: err
                });
            }
            else if (context.warn) {
                context.warn(message, err.loc ?? err.pos ?? null);
            }
        };
    }
    async function options(rawOptions) {
        const plugins = Array.isArray(rawOptions.plugins)
            ? [...rawOptions.plugins]
            : rawOptions.plugins
                ? [rawOptions.plugins]
                : [];
        plugins.unshift({
            name: 'rollup-plugin-external-globals--resolver',
            resolveId
        });
        return { ...rawOptions, plugins };
    }
    async function transform(code, id) {
        if ((!isVirtualModule(id) && !filter(id)) ||
            (isGlobalsObj && Object.keys(globals).every(moduleId => !code.includes(moduleId)))) {
            return null;
        }
        let ast;
        try {
            ast = this.parse(code);
        }
        catch (err) {
            getDebug(this)(err, `Failed to parse code, skip ${id}`);
            return null;
        }
        const magicString = new magic_string_1.default(code);
        const isTouched = await (0, import_to_globals_1.default)({
            ast,
            code: magicString,
            getName,
            getDynamicWrapper: dynamicWrapper,
            constBindings
        });
        return isTouched ? {
            code: magicString.toString(),
            map: magicString.generateMap()
        } : null;
    }
    return {
        name: "rollup-plugin-external-globals",
        options,
        transform,
    };
}
exports.default = createPlugin;
//# sourceMappingURL=index.js.map