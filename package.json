{"name": "rollup-plugin-external-globals", "version": "0.13.0", "description": "Transform external imports into global variables like output.globals.", "keywords": ["rollup-plugin", "es", "transform", "external", "globals"], "main": "index.js", "typings": "index.d.ts", "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}}, "files": ["lib", "*.d.ts"], "eslintIgnore": ["coverage"], "scripts": {"test": "eslint . --cache && c8 --reporter lcov mocha", "preversion": "npm test", "postversion": "git push --follow-tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/eight04/rollup-plugin-external-globals.git"}, "author": "eight04 <<EMAIL>>", "license": "MIT", "devDependencies": {"@eslint/js": "^9.9.0", "@rollup/plugin-commonjs": "^28.0.1", "c8": "^10.1.2", "endent": "^2.1.0", "eslint": "^9.5.0", "globals": "^15.9.0", "mocha": "^10.5.2", "rollup": "^4.18.0", "rollup2": "npm:rollup@^2.79.2", "tempdir-yaml": "^0.3.0"}, "dependencies": {"@rollup/pluginutils": "^5.1.0", "estree-walker": "^3.0.3", "is-reference": "^3.0.2", "magic-string": "^0.30.10"}, "peerDependencies": {"rollup": "^2.25.0 || ^3.3.0 || ^4.1.4"}}